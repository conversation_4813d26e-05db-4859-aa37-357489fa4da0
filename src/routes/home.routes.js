import {
    categoryItems,
    getbanner,
    dealOffers,
    bundleClearanceSale,
    brandWeek,
    getTopPicks,
    getTopSelling,
    getDealOfTheDay,
    getSaverZone,
    getELKData,
    categoryItemsOne,
    connectWithUs,
} from "../controllers/home.controllers.js";
import { connectWithUsSchema } from "../validators/home.validators.js";

export default async function homeRoutes(fastify) {
    fastify.get("/category_items", categoryItems);
    fastify.get("/category_items-1", categoryItemsOne);
    fastify.get("/getbannerlist", getbanner);
    fastify.get("/deal_offers", dealOffers);
    fastify.get("/bundle_clearance_sale", bundleClearanceSale);
    fastify.get("/brand_week", brandWeek);
    fastify.get("/getTopPicks", getTopPicks);
    fastify.get("/getTopSelling", getTopSelling);
    fastify.get("/getDealOfTheDay", getDealOfTheDay);
    fastify.get("/getSaverZone", getSaverZone);
    fastify.get("/get-elk-data", getELKData);
    fastify.post(
        "/connect-with-us",
        { schema: connectWithUsSchema },
        connectWithUs
    );
}
