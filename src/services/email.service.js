import nodemailer from "nodemailer";

const sendEmail = async (options) => {
    const transporter = nodemailer.createTransport({
        host: process.env.NODE_MAILER_HOST,
        port: process.env.NODE_MAILER_PORT,
        secure: process.env.NODE_MAILER_TLS_SECURE === "true",
        auth: {
            user: process.env.NODE_MAILER_EMAIL,
            pass: process.env.NODE_MAILER_PASS,
        },
        tls: {
            rejectUnauthorized: false,
        },
    });

    const mail = {
        from: process.env.NODE_MAILER_EMAIL,
        to: options.email,
        subject: options.subject,
        html: htmlContent,
        text: htmlContent,
    };

    try {
        await transporter.sendMail(mail);
    } catch (error) {
        // As sending email is not strongly coupled to the business logic it is not worth to raise an error when email sending fails
        // So it's better to fail silently rather than breaking the app
        console.error(
            "Email service failed silently. Make sure you have provided your MAILTRAP credentials in the .env file"
        );
        console.error("Error: ", error);
    }
};

export { sendEmail };
