const connectWithUsSchema = {
    body: {
        type: "object",
        required: ["email"],
        properties: {
            email: {
                type: "string",
                format: "email",
                minLength: 2,
                maxLength: 50,
                errorMessage: {
                    type: "Email must be a string",
                    format: "Please enter valid email address",
                    minLength: "Email must be at least 2 characters long",
                    maxLength: "Email must not exceed 50 characters",
                },
            },
        },
        errorMessage: {
            required: {
                email: "Email is required",
            },
        },
    },
};

export { connectWithUsSchema };
