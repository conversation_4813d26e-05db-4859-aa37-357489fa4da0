import { successResponse, errorResponse } from "./../utils/response.js";
import {
    categoryItemsService,
    getbannerService,
    getCarouselListService,
    multibannersListService,
    getExcitingOffersService,
    bundleClearanceSaleService,
    brandWeekService,
    getTopPicksService,
    getTopSellingService,
    getSaverZoneService,
    getDealOfTheDayService,
    getELKDataService,
    categoryItemsOneService,
    getDynamicBanners,
    connectWithUsService,
} from "../services/home.service.js";

export const categoryItemsOne = async (request, reply) => {
    try {
        const result = await categoryItemsService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error");
    }
};
export const categoryItems = async (request, reply) => {
    try {
        const result = await categoryItemsOneService(request);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error");
    }
};

export const getbanner = async (request, reply) => {
    try {
        const country_id = request.country_id;
        const banner = await getbannerService(country_id);
        const carousel = await getCarouselListService(country_id);
        const multibanners = await multibannersListService(country_id);
        const dynamicBanners = await getDynamicBanners(country_id);

        const finalOutput = {
            banner,
            carousel,
            multibanners,
            dynamicBanners,
        };
        return successResponse(
            reply,
            200,
            "All banner lists",
            "success",
            finalOutput
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error");
    }
};

// deal_offers
export const dealOffers = async (request, reply) => {
    try {
        const { country_id } = request;
        const result = await getExcitingOffersService(
            country_id,
            request.server
        );
        const return_output = {
            exciting_offers: result.exciting_offers,
            deal_of_the_day: result.deal_of_the_day,
            // "deal_of_the_day": result.deal_of_the_day
        };

        return successResponse(
            reply,
            200,
            "Exciting offers and Deal of the Day items",
            "success",
            return_output
        );
    } catch (err) {
        return errorResponse(reply, 500, err, "failure", []);
    }
};

// bundle_clearance_sale
export const bundleClearanceSale = async (request, reply) => {
    try {
        const { country_id } = request;
        const result = await bundleClearanceSaleService(
            country_id,
            request.server
        );

        return successResponse(
            reply,
            200,
            "Bundle products",
            "success",
            result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error");
    }
};

// brand_week
export const brandWeek = async (request, reply) => {
    try {
        const result = await brandWeekService(request);
        return successResponse(reply, 200, "Brand week", "success", result);
    } catch (err) {
        return errorResponse(reply, 500, err, "failure", []);
    }
};

export const getTopPicks = async (request, reply) => {
    try {
        const result = await getTopPicksService(request);
        return successResponse(reply, 200, "Top picks", "success", result);
    } catch (err) {
        return errorResponse(reply, 500, err, "failure", []);
    }
};

export const getTopSelling = async (request, reply) => {
    try {
        const result = await getTopSellingService(request);
        return successResponse(
            reply,
            200,
            "Top selling products",
            "success",
            result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, "failure", []);
    }
};

export const getDealOfTheDay = async (request, reply) => {
    try {
        const result = await getDealOfTheDayService(request);
        return successResponse(
            reply,
            200,
            "Deal of the day",
            "success",
            result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, "failure", []);
    }
};

export const getSaverZone = async (request, reply) => {
    try {
        const result = await getSaverZoneService(request);
        return successResponse(
            reply,
            200,
            "Saver zone products",
            "success",
            result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export const getELKData = async (request, reply) => {
    try {
        const result = await getELKDataService(request);
        return successResponse(
            reply,
            200,
            "Saver zone products",
            "success",
            result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

/**
 * Connect With Us
 */
export const connectWithUs = async (request, reply) => {
    try {
        const result = await connectWithUsService(request);
        return successResponse(
            reply,
            200,
            "Connect With Us",
            "success",
            result
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};
