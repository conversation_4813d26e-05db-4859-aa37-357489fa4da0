import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

export const CONNECT_US_REQUEST_STATUS = {
    NEW: "new",
    CONTACTED: "contacted",
    IGNORED: "ignored",
};

const ConnectUsRequest = sequelize.define(
    "ConnectUsRequest",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        email: {
            type: DataTypes.STRING(225),
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM(
                CONNECT_US_REQUEST_STATUS.NEW,
                CONNECT_US_REQUEST_STATUS.CONTACTED,
                CONNECT_US_REQUEST_STATUS.IGNORED
            ),
            allowNull: false,
            defaultValue: CONNECT_US_REQUEST_STATUS.NEW,
        },
        user_agent: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        ip_address: {
            type: DataTypes.STRING(45),
            allowNull: true,
        },
        created_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        updated_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
            onUpdate: DataTypes.NOW,
        },
    },
    {
        tableName: "connect_us_request",
        timestamps: false,
    }
);

export default ConnectUsRequest;
